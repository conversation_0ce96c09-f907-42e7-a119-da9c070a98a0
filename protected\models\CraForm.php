<?php
/**
 * CraForm class for CRA Request form validation
 */
class CraForm extends CFormModel
{
    public $requestIds;
    public $expositionLevel;
    public $expositionDetail;
    public $contentFiles;
    public $referencesFiles;
    public $proofDocuments;
    public $stradRiskAssessmentFiles;
    public $dateOfRequestingProofDocument;
    public $dateOfReceivingProofDocument;
    public $timeline;
    public $advertisementType;
    public $messages;
    public $status;

    /**
     * Declares the validation rules.
     */
    public function rules()
    {
        return array(
            array('expositionLevel, expositionDetail', 'required'),
            array('expositionLevel', 'in', 'range' => array('Low exposition', 'Medium exposition', 'High exposition')),
            array('expositionDetail', 'validateExpositionDetail'),
            array('requestIds, contentFiles, referencesFiles, proofDocuments, stradRiskAssessmentFiles, dateOfRequestingProofDocument, dateOfReceivingProofDocument, timeline, advertisementType, messages, status', 'safe'),
        );
    }

    /**
     * Custom validation for exposition detail based on exposition level
     */
    public function validateExpositionDetail($attribute, $params)
    {
        $validDetails = array(
            'Low exposition' => array('Point of sales', 'Press'),
            'Medium exposition' => array(
                'Back of the packaging, some social network, digital retailer',
                'L\'Oreal brand website, including a retailing part'
            ),
            'High exposition' => array('Facing of the packing, TVC, QVC (shopping channel)')
        );

        if (!empty($this->expositionLevel) && !empty($this->expositionDetail)) {
            if (!isset($validDetails[$this->expositionLevel]) ||
                !in_array($this->expositionDetail, $validDetails[$this->expositionLevel])) {
                $this->addError($attribute, 'Invalid exposition detail for the selected exposition level.');
            }
        }
    }

    /**
     * Declares attribute labels.
     */
    public function attributeLabels()
    {
        return array(
            'requestIds' => 'Request IDs',
            'expositionLevel' => 'Exposition Level',
            'expositionDetail' => 'Exposition Detail',
            'contentFiles' => 'Content Files',
            'referenceFiles' => 'Reference Files',
            'timeline' => 'Timeline',
            'advertisementType' => 'Advertisement Type',
            'messages' => 'Messages',
        );
    }

    /**
     * Get data array for API submission
     */
    public function getData()
    {
        $data = array();

        // Convert requestIds string to array if needed
        if (!empty($this->requestIds)) {
            if (is_string($this->requestIds)) {
                $data['requestIds'] = array_map('trim', explode(',', $this->requestIds));
            } else {
                $data['requestIds'] = $this->requestIds;
            }
        }

        // Required fields
        $data['expositionLevel'] = $this->expositionLevel;
        $data['expositionDetail'] = $this->expositionDetail;

        // Optional fields
        if (!empty($this->timeline)) {
            $data['timeline'] = $this->timeline;
        }

        if (!empty($this->advertisementType)) {
            $data['advertisementType'] = $this->advertisementType;
        }

        if (!empty($this->messages)) {
            $data['messages'] = $this->messages;
        }

        // File fields (processed by file upload methods)
        if (!empty($this->contentFiles)) {
            $data['contentFiles'] = $this->contentFiles;
        }

        if (!empty($this->referencesFiles)) {
            $data['referencesFiles'] = $this->referencesFiles;
        }

        if (!empty($this->proofDocuments)) {
            $data['proofDocuments'] = $this->proofDocuments;
        }

        if (!empty($this->stradRiskAssessmentFiles)) {
            $data['stradRiskAssessmentFiles'] = $this->stradRiskAssessmentFiles;
        }

        // Date fields for process workflow
        if (!empty($this->dateOfRequestingProofDocument)) {
            $data['dateOfRequestingProofDocument'] = $this->dateOfRequestingProofDocument;
        }

        if (!empty($this->dateOfReceivingProofDocument)) {
            $data['dateOfReceivingProofDocument'] = $this->dateOfReceivingProofDocument;
        }

        // Status field for workflow management
        if (!empty($this->status)) {
            $data['status'] = $this->status;
        }

        return $data;
    }
}