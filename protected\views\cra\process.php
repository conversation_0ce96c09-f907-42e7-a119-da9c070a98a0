<!-- content -->
<div id="content" class="app-content" role="main">

	<nav class="navbar navbar-controls bg-white">
		<div class="container-fluid">
			<!-- Brand and toggle get grouped for better mobile display -->
			<div class="navbar-header full">
				<ul class="nav navbar-nav nav-pills nav-list-button navbar-left">
					<li><button id="go-to-list" type="button" class="btn btn-default no-border navbar-btn navbar-left"><i class="fa fa-chevron-left"></i> Back</button></li>
					<li>
						<button type="button" data-toggle="dropdown" class="btn btn-default navbar-btn navbar-left">History <i class="caret"></i></button>
						<div class="dropdown-menu w-xxl">
							<div class="panel bg-white bg-inherit">
								<div class="panel-heading b-light bg-light">
								<strong>History</strong>
								</div>
								<div class="list-group list-group-alt" id="view-history">
									<?php
										$this->renderPartial('_view_cra_history',array('histories'=>$histories));
									?>
								</div>
							</div>
						</div>
					</li>

					<!-- SCI Groups Buttons -->
					<?php if(in_array(Yii::app()->user->groupName, array("SCI Manager", "SCI Staff"))): ?>
						<?php if($item->status != "COMPLETED"): ?>
						<li>
							<button type="button" data-toggle="dropdown" class="btn btn-default navbar-btn navbar-left">Reject <i class="caret"></i></button>
							<div class="dropdown-menu keep-dropdown w-xxl">
								<div class="panel bg-white bg-inherit">
									<div class="panel-heading b-light bg-light">
										<strong>Reason to reject</strong>
									</div>
									<div class="panel-body">
										<textarea class="form-control" name="reason-to-reject" id="reason-to-reject" rows="3"></textarea>
									</div>
									<div class="panel-footer text-right">
										<button class="btn btn-default cancel">Cancel</button>
										<button class="btn btn-primary" id="reject">Reject</button>
									</div>
								</div>
							</div>
						</li>
						<?php endif; ?>

						<?php if($item->status == "WAIT_FOR_STRAD_DIRECTOR_TO_ASSIGN"): ?>
						<li><button type="button" class="submit btn btn-default navbar-btn navbar-left" id="assign">Assign to STRAD Staff</button></li>
						<?php endif; ?>

						<?php if($item->status == "WAIT_FOR_STRAD_RISK_ASSESSMENT"): ?>
						<li><button type="button" class="submit btn btn-default navbar-btn navbar-left" id="save-risk-assessment">Save Risk Assessment</button></li>
						<?php endif; ?>

						<li><button type="button" class="submit btn btn-default navbar-btn navbar-left" id="save">Save</button></li>

						<?php if($item->status == "WAIT_FOR_APPROVAL"): ?>
						<li><button type="button" class="submit btn btn-default navbar-btn navbar-left" id="approve">Approve</button></li>
						<?php endif; ?>
					<?php endif; ?>
				</ul>
			</div>
		</div><!-- /.container-fluid -->
	</nav>

	<div class="p-md">
		<form id="cra-form" role="form" class="form-horizontal ng-pristine ng-valid" method="post" enctype="multipart/form-data" action="<?php echo Yii::app()->createUrl("cra/write");?>">
		<input type="hidden" name="typeAction" id="typeAction">
		<input type="hidden" name="id" id="id" value="<?php echo $item->id;?>">
			<h2>PROCESSING INFORMATION<?php if(isset($item->deleted)) echo ' (CRA IS DELETED AT '.date("H:i d-m-Y",$item->deleted/1000).')'?></h2>
			<?php
			$this->renderPartial('_process_form',array('item'=>$item,'requests'=>$requests));
			?>

			<h2>CLAIM RISK ASSESSMENT</h2>
			<?php
			$this->renderPartial('_claims_section',array(
				'item'=>$item,
				'claims'=>$claims,
				'robustnessOptions'=>$robustnessOptions,
				'finePenaltyOptions'=>$finePenaltyOptions,
				'showClaimForm'=>true
			));
			?>

			<h2>CRA DETAILS</h2>
			<?php
			$this->renderPartial('_form',array('item'=>$item,'requests'=>$requests,'typeForm'=>'process'));
			?>
		
		</form>
	</div>
</div>
<!-- / content -->

<!-- Note: Robustness and Fine/Penalty modals are now dynamically created by cra-claims.js -->

<script>
// Set authentication token for API calls
window.accessToken = '<?php echo isset(Yii::app()->session['token']) ? Yii::app()->session['token'] : ''; ?>';

// Wait for jQuery to be loaded before executing
function initializeCraProcessPage() {
	// Check if jQuery is available
	if (typeof $ === 'undefined') {
		// jQuery not loaded yet, wait and try again
		setTimeout(initializeCraProcessPage, 100);
		return;
	}

	$(document).ready(function() {
	// Navigation
	$('#go-to-list').click(function() {
		window.location.href = baseUrl + '/cra/index';
	});

	// Initialize CRA form functionality (same as update page)
	if (typeof initializeSaveButtons === 'function') {
		initializeSaveButtons();
	}

	// Note: Robustness and fine-penalty link handlers are now handled by cra-claims.js
	// which provides better error handling and uses cached dropdown data

	// Claim management handlers
	$(document).on('click', '.accept-claim, .reject-claim', function() {
		var claimId = $(this).data('claim-id');
		var action = $(this).hasClass('accept-claim') ? 'accepted' : 'not_accepted';

		$.ajax({
			url: baseUrl + '/cra/updateClaimAcceptance',
			type: 'POST',
			data: {
				claimId: claimId,
				action: action,
				date: new Date().toISOString().split('T')[0]
			},
			success: function(result) {
				if(result.success) {
					location.reload();
				}
			}
		});
	});

	$(document).on('click', '.edit-claim', function() {
		var claimId = $(this).data('claim-id');
		// TODO: Populate claim form with existing data
	});

	$(document).on('click', '.delete-claim', function() {
		var claimId = $(this).data('claim-id');
		if(confirm('Are you sure you want to delete this claim?')) {
			$.ajax({
				url: baseUrl + '/cra/deleteClaim',
				type: 'POST',
				data: { claimId: claimId },
				success: function(result) {
					if(result.success) {
						location.reload();
					}
				}
			});
		}
	});
});
}

// Start the initialization process
initializeCraProcessPage();
</script>
