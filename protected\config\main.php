<?php

// uncomment the following to define a path alias
// Yii::setPathOfAlias('local','path/to/local-folder');

// This is the main Web application configuration. Any writable
// CWebApplication properties can be configured here.
return array(
    'basePath' => dirname(__FILE__) . DIRECTORY_SEPARATOR . '..',
    'name' => 'Yii Blog Demo',

    // preloading 'log' component
    'preload' => array('log'),

    // autoloading model and component classes
    'import' => array(
        'application.models.*',
        'application.components.*',
        'application.services.*',
    ),

    'defaultController' => 'notification/index',

    'modules' => array(
        // uncomment the following to enable the Gii tool
        'gii' => array(
            'class' => 'system.gii.GiiModule',
            'password' => 'admin123',
            // If removed, Gii defaults to localhost only. Edit carefully to taste.
            //'ipFilters'=>array('*************','::1'),
        ),
    ),

    // application components
    'components' => array(
	
	
        'user' => array(
            // enable cookie-based authentication
            'allowAutoLogin' => true,
        ),
        /*
        'db'=>array(
            'connectionString' => 'sqlite:protected/data/blog.db',
            'tablePrefix' => 'tbl_',
        ),
        */
        // uncomment the following to use a MySQL database
        /*
        'db'=>array(
            'connectionString' => 'mysql:host=demo.jubiq.vn;dbname=drafts_loreal',
            'emulatePrepare' => true,
            'username' => 'drafts_loreal',
            'password' => 'NxZ2BQMF9xS4AEE5',
            'charset' => 'utf8',
            'tablePrefix' => 'tbl_',
        ),
        */
        'db' => array(
            'connectionString' => 'mysql:host=localhost:3309;dbname=loreal_drafts',
            'emulatePrepare' => true,
            'username' => 'root',
            'password' => 'haiha',
            'charset' => 'utf8',
            'tablePrefix' => 'tbl_',
        ),
        'errorHandler' => array(
            // use 'site/error' action to display errors
            'errorAction' => 'site/error',
        ),

        'urlManager' => array(
            'urlFormat' => 'path',
            'showScriptName' => false,
            'rules' => array(
                'approval/getRobustnessDetails/<id:\d+>' => 'approval/getRobustnessDetails',
                'approval/getFinePenaltyDetails/<id:\d+>' => 'approval/getFinePenaltyDetails',
                'notification/report/<startDate:\w+>/<endDate:\w+>' => 'notification/report',
                'file/download/<id:\d+>' => 'file/download',
                'notification/create/<id:\d+>' => 'notification/create',
                'notification/update/<id:\d+>' => 'notification/update',
                'notification/updateDrafts/<id:\d+>' => 'notification/updateDrafts',
                'notification/process/<id:\d+>' => 'notification/process',
                'notification/split/<id:\d+>' => 'notification/split',
                'notification/splitAll/<id:\d+>' => 'notification/splitAll',
                'notification/splitSku/<id:\d+>' => 'notification/splitSku',
                'label/create/<notification_id:\d+>' => 'label/create',
                'label/update/<id:\d+>' => 'label/update',
                'label/process/<id:\d+>' => 'label/process',
                'label/validate/<id:\d+>' => 'label/process',
                'label/reject/<id:\d+>' => 'label/process',
                'label/getRequestData/<id:\d+>' => 'label/getRequestData',
                'label/getIngredients' => 'label/getIngredients',
                'advertising/update/<id:\d+>' => 'advertising/update',
                'advertising/process/<id:\d+>' => 'advertising/process',
                'advertising/getRequestData/<id:\d+>' => 'advertising/getRequestData',
                'advertising/uploadFiles' => 'advertising/uploadFiles',
                'advertising/delete/<id:\d+>' => 'advertising/delete',
                'cra/update/<id:\d+>' => 'cra/update',
                'cra/process/<id:\d+>' => 'cra/process',
                'cra/getRequestData/<id:\d+>' => 'cra/getRequestData',
                'cra/uploadFiles' => 'cra/uploadFiles',
                'cra/delete/<id:\d+>' => 'cra/delete',
                'fineAndPenaltyManagement/read/<id:\d+>' => 'fineAndPenaltyManagement/read',
                'fineAndPenaltyManagement/delete/<id:\d+>' => 'fineAndPenaltyManagement/delete',
                'robustnessManagement/read/<id:\d+>' => 'robustnessManagement/read',
                'robustnessManagement/delete/<id:\d+>' => 'robustnessManagement/delete',
                '<controller:\w+>/read/<id:\d+>' => '<controller>/read',
                '<controller:\w+>/<action:\w+>/<page:\d+>' => '<controller>/<action>',
                '<controller:\w+>/<action:\w+>' => '<controller>/<action>',
            ),
        ),

        'log' => array(
            'class' => 'CLogRouter',
            'routes' => array(
                array(
                    'class' => 'CFileLogRoute',
                    'levels' => 'error, warning',
                ),
                // uncomment the following to show log messages on web pages
                /*
                                array(
                                    'class'=>'CWebLogRoute',
                                ),
                */
            ),
        ),
    ),

    // application-level parameters that can be accessed
    // using Yii::app()->params['paramName']
    'params' => require(dirname(__FILE__) . '/params.php'),
);